"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";
import { useSession } from "@/lib/auth-client";
import { posthogCookieUtils, getAllCookies } from "@/lib/cookie-utils";
import { env } from "@/env";

/**
 * Debug component for PostHog anonymous tracking
 * Only shows in development mode
 */
export function PostHogDebug() {
  const [cookies, setCookies] = useState<Record<string, string>>({});
  const [mounted, setMounted] = useState(false);
  
  const { data: session } = useSession();
  const {
    anonymousId,
    isAnonymous,
    isAnonymousTrackingReady,
    captureAnonymous,
    trackWaitlistSignup,
    getFeatureFlag,
    isFeatureEnabled,
    clearAnonymousData,
    getCurrentAnonymousId,
  } = usePostHogAnonymous();

  useEffect(() => {
    setMounted(true);
    setCookies(getAllCookies());
  }, []);

  const refreshCookies = () => {
    setCookies(getAllCookies());
  };

  const testEvent = () => {
    captureAnonymous("debug_test_event", {
      test_property: "test_value",
      timestamp: new Date().toISOString(),
    });
  };

  const testWaitlistSignup = () => {
    const testEmail = `test+${Date.now()}@example.com`;
    trackWaitlistSignup(testEmail, {
      signup_source: "debug_test",
      test_signup: true,
    });
  };

  const testFeatureFlag = () => {
    const flagValue = getFeatureFlag("debug_test_flag", "default");
    captureAnonymous("debug_feature_flag_test", {
      flag_key: "debug_test_flag",
      flag_value: flagValue,
    });
    alert(`Feature flag value: ${flagValue}`);
  };

  if (!mounted || process.env.NODE_ENV !== "development") {
    return null;
  }

  const postHogCookieName = `ph_${env.NEXT_PUBLIC_POSTHOG_KEY}_posthog`;
  const postHogDistinctId = posthogCookieUtils.getDistinctId(env.NEXT_PUBLIC_POSTHOG_KEY);

  return (
    <Card className="fixed bottom-4 right-4 w-96 max-h-96 overflow-auto z-50 bg-background/95 backdrop-blur">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center justify-between">
          PostHog Debug
          <Badge variant={isAnonymousTrackingReady ? "default" : "destructive"}>
            {isAnonymousTrackingReady ? "Ready" : "Not Ready"}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3 text-xs">
        {/* User Status */}
        <div>
          <strong>User Status:</strong>
          <div className="ml-2">
            <div>Authenticated: {session?.user ? "Yes" : "No"}</div>
            <div>Anonymous: {isAnonymous ? "Yes" : "No"}</div>
            <div>User ID: {session?.user?.id || "None"}</div>
          </div>
        </div>

        {/* Anonymous Tracking */}
        <div>
          <strong>Anonymous Tracking:</strong>
          <div className="ml-2">
            <div>Anonymous ID: {getCurrentAnonymousId() || "None"}</div>
            <div>Tracking Ready: {isAnonymousTrackingReady ? "Yes" : "No"}</div>
            <div>PostHog Distinct ID: {postHogDistinctId || "None"}</div>
          </div>
        </div>

        {/* Cookies */}
        <div>
          <strong>Relevant Cookies:</strong>
          <div className="ml-2 max-h-20 overflow-auto">
            <div>tc_anonymous_id: {cookies.tc_anonymous_id || "None"}</div>
            <div>{postHogCookieName}: {cookies[postHogCookieName] ? "Present" : "None"}</div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="space-y-2">
          <strong>Test Actions:</strong>
          <div className="grid grid-cols-2 gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={testEvent}
              disabled={!isAnonymousTrackingReady}
            >
              Test Event
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={testWaitlistSignup}
              disabled={!isAnonymousTrackingReady}
            >
              Test Waitlist
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={testFeatureFlag}
              disabled={!isAnonymousTrackingReady}
            >
              Test Flag
            </Button>
            
            <Button 
              size="sm" 
              variant="outline" 
              onClick={refreshCookies}
            >
              Refresh
            </Button>
          </div>
        </div>

        {/* Danger Zone */}
        <div className="border-t pt-2">
          <strong className="text-destructive">Danger Zone:</strong>
          <div className="mt-1">
            <Button 
              size="sm" 
              variant="destructive" 
              onClick={clearAnonymousData}
            >
              Clear Anonymous Data
            </Button>
          </div>
        </div>

        {/* Feature Flags Test */}
        <div>
          <strong>Feature Flags:</strong>
          <div className="ml-2">
            <div>show_beta_features: {isFeatureEnabled("show_beta_features") ? "Yes" : "No"}</div>
            <div>debug_test_flag: {getFeatureFlag("debug_test_flag", "default")}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Minimal debug info for production (if needed)
 */
export function PostHogDebugMinimal() {
  const { isAnonymousTrackingReady, getCurrentAnonymousId } = usePostHogAnonymous();
  
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 bg-background/90 backdrop-blur p-2 rounded text-xs border">
      <div>Anonymous ID: {getCurrentAnonymousId()?.slice(0, 8)}...</div>
      <div>Ready: {isAnonymousTrackingReady ? "✓" : "✗"}</div>
    </div>
  );
}
