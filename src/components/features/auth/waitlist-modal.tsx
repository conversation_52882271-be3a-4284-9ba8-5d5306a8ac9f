"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { usePostHog } from "posthog-js/react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useTRPC } from "@/components/integrations/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { type WaitlistFormData, waitlistSchema } from "@/lib/schema";

interface WaitlistModalProps {
  isOpen: boolean;
  onClose: () => void;
  accountType?: "homeowner" | "contractor";
  onSubmitSuccess?: () => void;
}

export function WaitlistModal({
  isOpen,
  onClose,
  accountType,
  onSubmitSuccess,
}: WaitlistModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const posthog = usePostHog();
  const trpc = useTRPC();

  const form = useForm({
    resolver: zodResolver(waitlistSchema),
    defaultValues: {
      email: "",
      name: "",
      reason: "",
      accountType: accountType || "homeowner",
    },
  });

  const joinWaitlist = useMutation(trpc.waitlist.join.mutationOptions());

  // Update the account type when the prop changes
  useEffect(() => {
    if (accountType) {
      form.setValue("accountType", accountType);
    }
  }, [accountType, form]);

  async function onSubmit(values: WaitlistFormData) {
    setIsSubmitting(true);

    try {
      // Capture the waitlist submission in PostHog
      posthog.capture("waitlist_submission", {
        email: values.email,
        name: values.name,
        reason: values.reason || "",
        account_type: values.accountType,
      });

      // Submit to the API
      await joinWaitlist.mutateAsync({
        name: values.name,
        email: values.email,
        reason: values.reason,
        accountType: values.accountType,
      });

      toast.success("You've been added to our waitlist!", {
        description: "We'll notify you when you can join.",
      });

      if (onSubmitSuccess) {
        onSubmitSuccess();
      }

      onClose();
    } catch (error: unknown) {
      console.error("Error submitting to waitlist:", error);

      // Type guard to check if error is a TRPC error object
      if (
        typeof error === "object" &&
        error !== null &&
        "data" in error &&
        typeof error.data === "object" &&
        error.data !== null &&
        "code" in error.data &&
        error.data.code === "CONFLICT"
      ) {
        toast.info("You're already on our waitlist!", {
          description: "We'll notify you when you can join.",
        });

        if (onSubmitSuccess) {
          onSubmitSuccess();
        }

        onClose();
        return;
      }

      // Extract error message if available
      const errorMessage =
        typeof error === "object" &&
        error !== null &&
        "message" in error &&
        typeof error.message === "string"
          ? error.message
          : "Please try again later.";

      toast.error("Failed to join waitlist", {
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Join our waitlist</DialogTitle>
          <DialogDescription>
            We're currently in a limited access phase. Join our waitlist to be
            notified when you can create an account.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="John Doe" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      {...field}
                      placeholder="<EMAIL>"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Why are you interested? (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Tell us why you're interested in joining..."
                      className="resize-none"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Join Waitlist"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
