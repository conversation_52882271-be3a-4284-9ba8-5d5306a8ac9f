import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { waitlist } from "@/db/schema";
import { waitlistSchema } from "@/lib/schema";
import { adminProcedure, publicProcedure, router } from "../procedures";

export const waitlistRouter = router({
  // Public procedure to join the waitlist
  join: publicProcedure.input(waitlistSchema).mutation(async ({ input }) => {
    try {
      // Check if email already exists in the waitlist
      const existingEntry = await db
        .select()
        .from(waitlist)
        .where(eq(waitlist.email, input.email))
        .limit(1);

      if (existingEntry.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "This email is already on the waitlist",
        });
      }

      // Insert new waitlist entry
      const result = await db
        .insert(waitlist)
        .values({
          name: input.name,
          email: input.email,
          reason: input.reason || null,
          accountType: input.accountType,
        })
        .returning();

      return {
        success: true,
        message: "Successfully joined the waitlist",
        entry: result[0],
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Error joining waitlist:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to join waitlist",
      });
    }
  }),

  // Admin procedures
  list: adminProcedure
    .input(
      z.object({
        status: z
          .enum(["PENDING", "APPROVED", "REJECTED", "INVITED"])
          .optional(),
        accountType: z.enum(["homeowner", "contractor"]).optional(),
        limit: z.number().min(1).max(100).default(50),
        offset: z.number().default(0),
      }),
    )
    .query(async ({ input }) => {
      try {
        const query = db
          .select()
          .from(waitlist)
          .where(
            and(
              input.status ? eq(waitlist.status, input.status) : undefined,
              input.accountType
                ? eq(waitlist.accountType, input.accountType)
                : undefined,
            ),
          )
          .limit(input.limit)
          .offset(input.offset)
          .orderBy(waitlist.createdAt);

        const entries = await query;
        const total = await db
          .select({ count: waitlist.id })
          .from(waitlist)
          .where(
            and(
              input.status ? eq(waitlist.status, input.status) : undefined,
              input.accountType
                ? eq(waitlist.accountType, input.accountType)
                : undefined,
            ),
          )
          .then((result) => result.length);

        return {
          entries,
          total,
          hasMore: input.offset + input.limit < total,
        };
      } catch (error) {
        console.error("Error listing waitlist entries:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to list waitlist entries",
        });
      }
    }),

  getById: adminProcedure.input(z.string()).query(async ({ input }) => {
    try {
      const entry = await db
        .select()
        .from(waitlist)
        .where(eq(waitlist.id, input))
        .limit(1);

      if (entry.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Waitlist entry not found",
        });
      }

      return entry[0];
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error("Error getting waitlist entry:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get waitlist entry",
      });
    }
  }),

  updateStatus: adminProcedure
    .input(
      z.object({
        id: z.string(),
        status: z.enum(["PENDING", "APPROVED", "REJECTED", "INVITED"]),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        const result = await db
          .update(waitlist)
          .set({
            status: input.status,
            updatedAt: new Date(),
            ...(input.status === "INVITED"
              ? {
                  invitedAt: new Date(),
                  inviteToken: crypto.randomUUID(),
                  inviteExpires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
                }
              : {}),
          })
          .where(eq(waitlist.id, input.id))
          .returning();

        if (result.length === 0) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Waitlist entry not found",
          });
        }

        return {
          success: true,
          message: `Status updated to ${input.status}`,
          entry: result[0],
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error("Error updating waitlist status:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update waitlist status",
        });
      }
    }),

  delete: adminProcedure.input(z.string()).mutation(async ({ input }) => {
    try {
      const result = await db
        .delete(waitlist)
        .where(eq(waitlist.id, input))
        .returning();

      if (result.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Waitlist entry not found",
        });
      }

      return {
        success: true,
        message: "Waitlist entry deleted",
      };
    } catch (error) {
      console.error("Error deleting waitlist entry:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete waitlist entry",
      });
    }
  }),
});
