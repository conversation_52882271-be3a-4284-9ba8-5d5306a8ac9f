# PostHog Anonymous User Tracking

This document explains how to use the PostHog anonymous user tracking system for waitlist users and other anonymous interactions.

## Overview

The anonymous tracking system provides:
- Persistent anonymous IDs stored in cookies
- Seamless transition from anonymous to identified users
- Server-side and client-side tracking utilities
- Feature flag support for anonymous users
- Proper event attribution and user journey tracking

## Architecture

### Components

1. **<PERSON>ie Utils** (`src/lib/cookie-utils.ts`)
   - Client-side cookie management
   - Anonymous ID generation and persistence
   - PostHog cookie parsing utilities

2. **PostHog Provider** (`src/components/providers/posthog-provider.tsx`)
   - Automatic anonymous ID initialization
   - User identification and aliasing
   - Session management

3. **Client Hook** (`src/hooks/use-posthog-anonymous.ts`)
   - React hook for anonymous tracking
   - Event capture utilities
   - Feature flag access

4. **Server Utils** (`src/lib/posthog-anonymous-server.ts`)
   - Server-side anonymous tracking
   - Cookie parsing in server components
   - API route support

## Usage

### Client-Side Tracking

#### Basic Event Tracking

```tsx
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";

function MyComponent() {
  const { captureAnonymous, isAnonymousTrackingReady } = usePostHogAnonymous();

  const handleClick = () => {
    if (isAnonymousTrackingReady) {
      captureAnonymous("button_clicked", {
        button_name: "hero_cta",
        page: window.location.pathname,
      });
    }
  };

  return <button onClick={handleClick}>Click Me</button>;
}
```

#### Waitlist Signup

```tsx
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";

function WaitlistForm() {
  const { trackWaitlistSignup } = usePostHogAnonymous();

  const handleSubmit = (email: string) => {
    trackWaitlistSignup(email, {
      signup_source: "landing_page",
      feature_interest: ["project_management", "team_collaboration"],
    });
  };

  // ... form implementation
}
```

#### Feature Flags for Anonymous Users

```tsx
import { usePostHogAnonymous } from "@/hooks/use-posthog-anonymous";

function FeatureComponent() {
  const { isFeatureEnabled, getFeatureFlag } = usePostHogAnonymous();

  const showBetaFeatures = isFeatureEnabled("show_beta_features");
  const ctaVariant = getFeatureFlag("cta_variant", "default");

  return (
    <div>
      {showBetaFeatures && <BetaFeature />}
      <Button variant={ctaVariant === "primary" ? "default" : "outline"}>
        {ctaVariant === "primary" ? "Get Started" : "Learn More"}
      </Button>
    </div>
  );
}
```

### Server-Side Tracking

#### API Routes

```tsx
// app/api/waitlist/route.ts
import { trackWaitlistSignupServer } from "@/lib/posthog-anonymous-server";

export async function POST(request: Request) {
  const { email } = await request.json();

  // Track the signup server-side
  await trackWaitlistSignupServer(email, {
    signup_source: "api",
    api_version: "v1",
  });

  return Response.json({ success: true });
}
```

#### Server Components

```tsx
// app/page.tsx
import { getFeatureFlagServer } from "@/lib/posthog-anonymous-server";

export default async function HomePage() {
  const showBetaFeatures = await getFeatureFlagServer("show_beta_features");

  return (
    <div>
      <h1>Welcome</h1>
      {showBetaFeatures && <BetaSection />}
    </div>
  );
}
```

#### Middleware

```tsx
// middleware.ts
import { getAnonymousIdFromRequest } from "@/lib/posthog-anonymous-server";

export function middleware(request: NextRequest) {
  const anonymousId = getAnonymousIdFromRequest(request);
  
  // Use anonymousId for feature flag evaluation, etc.
  
  return NextResponse.next();
}
```

## Cookie Management

### Anonymous ID Cookie

- **Name**: `tc_anonymous_id`
- **Duration**: 1 year
- **Purpose**: Persistent anonymous identification
- **Scope**: Entire domain

### PostHog Cookie

- **Name**: `ph_{PROJECT_KEY}_posthog`
- **Duration**: Set by PostHog
- **Purpose**: PostHog session and user data
- **Scope**: Entire domain

## User Journey Flow

1. **Anonymous User Visits**
   - Anonymous ID generated and stored in cookie
   - PostHog initialized with anonymous ID
   - Events tracked with anonymous context

2. **User Joins Waitlist**
   - Email collected and associated with anonymous ID
   - User identified in PostHog with email
   - Anonymous ID preserved for continuity

3. **User Signs Up/Logs In**
   - User authenticated with real user ID
   - Anonymous ID aliased to authenticated user ID
   - Historical anonymous events linked to user

4. **User Logs Out**
   - PostHog reset to clear identification
   - New anonymous ID generated for future tracking

## Event Properties

### Automatic Properties

All anonymous events include:
- `$is_anonymous`: `true`
- `$anonymous_id`: The persistent anonymous ID
- `$user_type`: `"anonymous"` or `"waitlist"`

### Waitlist Events

Waitlist signup events include:
- `email`: User's email address
- `signup_source`: Where the signup originated
- `referrer`: HTTP referrer
- `user_agent`: Browser user agent
- `landing_page`: Page where signup occurred

## Feature Flags

Anonymous users can access feature flags using their anonymous ID:

```tsx
// Client-side
const { getFeatureFlag } = usePostHogAnonymous();
const flagValue = getFeatureFlag("my_flag", "default");

// Server-side
const flagValue = await getFeatureFlagServer("my_flag", "default");
```

## Privacy Considerations

- Anonymous IDs are generated client-side using `crypto.randomUUID()`
- No personally identifiable information in anonymous tracking
- Users can clear tracking data via browser cookie deletion
- GDPR/CCPA compliant when used properly

## Testing

### Development Mode

In development, debug information is available:

```tsx
const { getCurrentAnonymousId, isAnonymousTrackingReady } = usePostHogAnonymous();

console.log("Anonymous ID:", getCurrentAnonymousId());
console.log("Tracking Ready:", isAnonymousTrackingReady);
```

### Clear Tracking Data

```tsx
const { clearAnonymousData } = usePostHogAnonymous();

// Clear all anonymous tracking data
clearAnonymousData();
```

## Best Practices

1. **Always Check Tracking Ready State**
   ```tsx
   if (isAnonymousTrackingReady) {
     captureAnonymous("event_name", properties);
   }
   ```

2. **Use Descriptive Event Names**
   ```tsx
   // Good
   captureAnonymous("waitlist_signup_completed", { source: "hero" });
   
   // Bad
   captureAnonymous("click", { type: "signup" });
   ```

3. **Include Context Properties**
   ```tsx
   captureAnonymous("feature_clicked", {
     feature_name: "project_management",
     page: window.location.pathname,
     section: "features_grid",
   });
   ```

4. **Handle Errors Gracefully**
   ```tsx
   try {
     trackWaitlistSignup(email, properties);
   } catch (error) {
     console.error("Tracking error:", error);
     // Continue with user flow
   }
   ```

## Troubleshooting

### Common Issues

1. **Events Not Appearing**
   - Check `isAnonymousTrackingReady` state
   - Verify PostHog configuration
   - Check browser console for errors

2. **Anonymous ID Not Persisting**
   - Check cookie settings and browser privacy mode
   - Verify domain configuration
   - Check for cookie blocking extensions

3. **Feature Flags Not Working**
   - Ensure anonymous ID is properly set
   - Check flag configuration in PostHog
   - Verify server-side flag evaluation

### Debug Tools

```tsx
// Client-side debugging
const debug = {
  anonymousId: getCurrentAnonymousId(),
  isReady: isAnonymousTrackingReady,
  isAnonymous: isAnonymous,
};
console.log("PostHog Anonymous Debug:", debug);
```

## Migration Guide

If you're adding this to an existing application:

1. Install the new utilities
2. Update PostHog provider
3. Replace direct PostHog calls with anonymous utilities
4. Test user journey flows
5. Monitor events in PostHog dashboard

## Support

For issues or questions:
- Check PostHog documentation
- Review browser console for errors
- Test in incognito mode to verify cookie behavior
- Verify environment variables are set correctly
